#!/usr/bin/env node

const fs = require('fs');

/**
 * 从过滤后的 Figma 数据中提取按钮尺寸信息
 */
class ButtonDimensionExtractor {
    constructor(filteredDataPath) {
        this.data = this.loadFilteredData(filteredDataPath);
    }

    loadFilteredData(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }
            const fileContent = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(fileContent);
        } catch (error) {
            throw new Error(`加载过滤数据失败: ${error.message}`);
        }
    }

    /**
     * 提取所有按钮组件的尺寸信息
     */
    extractButtonDimensions() {
        const buttonDimensions = [];
        const components = this.data.metadata?.components || {};
        const globalStyles = this.data.globalVars?.styles || {};

        // 遍历所有按钮组件
        Object.entries(components).forEach(([componentId, component]) => {
            const buttonInfo = {
                id: componentId,
                name: component.name,
                componentSetId: component.componentSetId,
                dimensions: null,
                borderRadius: null,
                category: this.categorizeButton(component.name)
            };

            // 查找对应的尺寸信息
            // 通常按钮的尺寸信息存储在 globalStyles 中
            const relatedStyles = this.findRelatedStyles(componentId, globalStyles);
            if (relatedStyles.length > 0) {
                buttonInfo.dimensions = relatedStyles.map(style => ({
                    styleId: style.id,
                    width: style.dimensions?.width,
                    height: style.dimensions?.height,
                    location: style.locationRelativeToParent
                }));
            }

            // 提取圆角信息（从组件名称中推断）
            buttonInfo.borderRadius = this.extractBorderRadius(component.name);

            buttonDimensions.push(buttonInfo);
        });

        return buttonDimensions;
    }

    /**
     * 查找与组件相关的样式
     */
    findRelatedStyles(componentId, globalStyles) {
        const relatedStyles = [];
        
        Object.entries(globalStyles).forEach(([styleId, styleData]) => {
            if (styleData.dimensions) {
                relatedStyles.push({
                    id: styleId,
                    dimensions: styleData.dimensions,
                    locationRelativeToParent: styleData.locationRelativeToParent,
                    mode: styleData.mode,
                    sizing: styleData.sizing
                });
            }
        });

        return relatedStyles;
    }

    /**
     * 从组件名称中提取圆角信息
     */
    extractBorderRadius(componentName) {
        if (componentName.includes('小按钮')) return '12px';
        if (componentName.includes('中按钮')) return '15px';
        if (componentName.includes('大按钮')) return '22px';
        if (componentName.includes('纯文字')) return '2px';
        return 'unknown';
    }

    /**
     * 按钮分类
     */
    categorizeButton(componentName) {
        const categories = {
            size: 'unknown',
            width: 'unknown',
            color: 'unknown',
            style: 'unknown',
            state: 'unknown'
        };

        // 尺寸分类
        if (componentName.includes('小按钮')) categories.size = 'small';
        else if (componentName.includes('中按钮')) categories.size = 'medium';
        else if (componentName.includes('大按钮')) categories.size = 'large';

        // 宽度分类
        if (componentName.includes('短')) categories.width = 'short';
        else if (componentName.includes('中')) categories.width = 'medium';
        else if (componentName.includes('长')) categories.width = 'long';

        // 颜色分类
        if (componentName.includes('粉')) categories.color = 'pink';
        else if (componentName.includes('黄')) categories.color = 'yellow';
        else if (componentName.includes('蓝')) categories.color = 'blue';
        else if (componentName.includes('灰')) categories.color = 'gray';
        else if (componentName.includes('白')) categories.color = 'white';

        // 样式分类
        if (componentName.includes('实色')) categories.style = 'solid';
        else if (componentName.includes('线框')) categories.style = 'outline';
        else if (componentName.includes('浅底')) categories.style = 'light';

        // 状态分类
        if (componentName.includes('正常')) categories.state = 'normal';
        else if (componentName.includes('按下')) categories.state = 'pressed';
        else if (componentName.includes('禁用')) categories.state = 'disabled';

        return categories;
    }

    /**
     * 生成按钮尺寸统计报告
     */
    generateDimensionReport() {
        const buttonDimensions = this.extractButtonDimensions();
        const report = {
            totalButtons: buttonDimensions.length,
            sizeDistribution: {},
            dimensionSummary: [],
            borderRadiusMap: {}
        };

        // 统计尺寸分布
        buttonDimensions.forEach(button => {
            const size = button.category.size;
            if (!report.sizeDistribution[size]) {
                report.sizeDistribution[size] = 0;
            }
            report.sizeDistribution[size]++;

            // 统计圆角
            const radius = button.borderRadius;
            if (!report.borderRadiusMap[radius]) {
                report.borderRadiusMap[radius] = [];
            }
            report.borderRadiusMap[radius].push({
                name: button.name,
                size: size
            });
        });

        // 生成尺寸摘要
        const uniqueDimensions = new Set();
        buttonDimensions.forEach(button => {
            if (button.dimensions && button.dimensions.length > 0) {
                button.dimensions.forEach(dim => {
                    if (dim.width && dim.height) {
                        uniqueDimensions.add(`${dim.width}x${dim.height}`);
                    }
                });
            }
        });

        report.dimensionSummary = Array.from(uniqueDimensions).sort();

        return {
            report,
            detailedData: buttonDimensions
        };
    }

    /**
     * 输出格式化的报告
     */
    printReport() {
        const { report, detailedData } = this.generateDimensionReport();

        console.log('\n=== 按钮尺寸分析报告 ===');
        console.log(`总按钮数量: ${report.totalButtons}`);
        
        console.log('\n📏 按钮尺寸分布:');
        Object.entries(report.sizeDistribution).forEach(([size, count]) => {
            console.log(`  ${size}: ${count} 个`);
        });

        console.log('\n🔘 圆角大小映射:');
        Object.entries(report.borderRadiusMap).forEach(([radius, buttons]) => {
            console.log(`  ${radius}: ${buttons.length} 个按钮`);
            buttons.slice(0, 3).forEach(btn => {
                console.log(`    - ${btn.name}`);
            });
            if (buttons.length > 3) {
                console.log(`    ... 还有 ${buttons.length - 3} 个`);
            }
        });

        console.log('\n📐 发现的尺寸规格:');
        report.dimensionSummary.slice(0, 10).forEach(dim => {
            console.log(`  ${dim}`);
        });
        if (report.dimensionSummary.length > 10) {
            console.log(`  ... 还有 ${report.dimensionSummary.length - 10} 种尺寸`);
        }

        return { report, detailedData };
    }
}

/**
 * 主函数
 */
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('用法: node extractButtonDimensions.js <过滤后的JSON文件路径>');
        console.log('示例: node extractButtonDimensions.js filtered_data_按钮.json');
        process.exit(1);
    }

    const filteredDataPath = args[0];

    try {
        const extractor = new ButtonDimensionExtractor(filteredDataPath);
        const { report, detailedData } = extractor.printReport();

        // 保存详细数据
        const outputFile = 'button_dimensions_analysis.json';
        fs.writeFileSync(outputFile, JSON.stringify({
            report,
            detailedData
        }, null, 2), 'utf8');
        
        console.log(`\n✅ 详细分析数据已保存到: ${outputFile}`);

    } catch (error) {
        console.error('分析按钮尺寸时发生错误:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main();
}

module.exports = { ButtonDimensionExtractor };
