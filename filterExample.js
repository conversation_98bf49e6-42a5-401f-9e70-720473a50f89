#!/usr/bin/env node

const { FigmaDataFilter } = require('./filterFigmaData');

/**
 * 使用示例：演示如何使用 FigmaDataFilter
 */
async function demonstrateFiltering() {
    console.log('=== Figma 数据过滤器使用示例 ===\n');

    try {
        // 创建过滤器实例
        const filter = new FigmaDataFilter('./figma.yml');

        // 示例1: 过滤按钮相关数据
        console.log('示例1: 过滤按钮相关数据');
        console.log('关键词: ["按钮", "Button"]');
        const buttonData = filter.filterByKeywords(['按钮', 'Button']);
        
        console.log('按钮数据统计:');
        console.log('- 组件:', Object.keys(buttonData.metadata.components).length);
        console.log('- 布局:', Object.keys(buttonData.layouts).length);
        console.log('- 节点:', filter.countNodes(buttonData.nodes));
        
        // 显示找到的按钮组件名称
        console.log('\n找到的按钮组件:');
        Object.values(buttonData.metadata.components).forEach((comp, index) => {
            console.log(`  ${index + 1}. ${comp.name}`);
        });

        console.log(JSON.stringify(buttonData))

        // 示例2: 过滤特定尺寸的按钮
        console.log('\n' + '='.repeat(50));
        console.log('示例2: 过滤小按钮数据');
        console.log('关键词: ["小按钮", "small"]');
        const smallButtonData = filter.filterByKeywords(['小按钮', 'small']);
        
        console.log('小按钮数据统计:');
        console.log('- 组件:', Object.keys(smallButtonData.metadata.components).length);
        console.log('- 布局:', Object.keys(smallButtonData.layouts).length);
        
        // 显示尺寸信息
        console.log('\n小按钮尺寸信息:');
        Object.entries(smallButtonData.layouts).forEach(([layoutId, layout]) => {
            if (layout.dimensions) {
                console.log(`  ${layoutId}: ${layout.dimensions.width}px × ${layout.dimensions.height}px`);
            }
        });

        // 示例3: 过滤多个关键词
        console.log('\n' + '='.repeat(50));
        console.log('示例3: 过滤多个关键词');
        console.log('关键词: ["按钮", "实色", "正常"]');
        const multiKeywordData = filter.filterByKeywords(['按钮', '实色', '正常']);
        
        console.log('多关键词过滤统计:');
        console.log('- 组件:', Object.keys(multiKeywordData.metadata.components).length);
        console.log('- 布局:', Object.keys(multiKeywordData.layouts).length);

        // 示例4: 数据大小对比
        console.log('\n' + '='.repeat(50));
        console.log('示例4: 数据大小对比');
        const sizeInfo = filter.getDataSize();
        console.log(`原始数据大小: ${(sizeInfo.original / 1024).toFixed(2)} KB`);
        console.log(`过滤后大小: ${(sizeInfo.filtered / 1024).toFixed(2)} KB`);
        console.log(`减少比例: ${sizeInfo.reduction}`);

        // 示例5: 保存不同格式的过滤数据
        console.log('\n' + '='.repeat(50));
        console.log('示例5: 保存过滤数据');
        
        // 保存按钮数据（用于 LLM 分析）
        saveForLLMAnalysis(buttonData, 'button_data_for_llm.json');
        
        // 保存尺寸摘要（更紧凑的格式）
        const dimensionSummary = createDimensionSummary(buttonData);
        saveForLLMAnalysis(dimensionSummary, 'button_dimensions_summary.json');
        
        console.log('✅ 示例完成！');

    } catch (error) {
        console.error('示例运行失败:', error.message);
    }
}

/**
 * 为 LLM 分析保存数据
 */
function saveForLLMAnalysis(data, filename) {
    const fs = require('fs');
    
    const llmData = {
        extractedAt: new Date().toISOString(),
        purpose: 'LLM Analysis - Token Optimized',
        data: data
    };
    
    fs.writeFileSync(filename, JSON.stringify(llmData, null, 2), 'utf8');
    console.log(`✅ LLM 分析数据已保存到: ${filename}`);
}

/**
 * 创建尺寸摘要（更紧凑的格式）
 */
function createDimensionSummary(filteredData) {
    const summary = {
        components: {},
        dimensions: {},
        relationships: []
    };

    // 提取组件信息
    Object.entries(filteredData.metadata.components).forEach(([id, comp]) => {
        summary.components[id] = {
            name: comp.name,
            key: comp.key
        };
    });

    // 提取尺寸信息
    Object.entries(filteredData.layouts).forEach(([layoutId, layout]) => {
        if (layout.dimensions) {
            summary.dimensions[layoutId] = {
                width: layout.dimensions.width,
                height: layout.dimensions.height,
                size: `${layout.dimensions.width}x${layout.dimensions.height}`
            };
        }
    });

    // 建立关系映射
    const traverseNodes = (nodes) => {
        if (!Array.isArray(nodes)) return;
        
        nodes.forEach(node => {
            if (node.componentId && node.layout) {
                summary.relationships.push({
                    componentId: node.componentId,
                    layoutId: node.layout,
                    nodeId: node.id
                });
            }
            
            if (node.children) {
                traverseNodes(node.children);
            }
        });
    };

    traverseNodes(filteredData.nodes);

    return summary;
}

/**
 * 按钮尺寸专用过滤器
 */
function filterButtonDimensions(keywords = ['按钮', 'Button']) {
    console.log('\n=== 专用按钮尺寸过滤器 ===');
    
    const filter = new FigmaDataFilter('./figma.yml');
    const buttonData = filter.filterByKeywords(keywords);
    
    // 按尺寸分组
    const dimensionGroups = {
        small: [],    // 高度 <= 24px
        medium: [],   // 高度 25-35px  
        large: []     // 高度 > 35px
    };

    Object.entries(buttonData.layouts).forEach(([layoutId, layout]) => {
        if (layout.dimensions) {
            const height = layout.dimensions.height;
            const width = layout.dimensions.width;
            
            const dimInfo = {
                layoutId,
                width,
                height,
                size: `${width}x${height}`
            };

            if (height <= 24) {
                dimensionGroups.small.push(dimInfo);
            } else if (height <= 35) {
                dimensionGroups.medium.push(dimInfo);
            } else {
                dimensionGroups.large.push(dimInfo);
            }
        }
    });

    console.log('按钮尺寸分组结果:');
    console.log(`小按钮 (≤24px): ${dimensionGroups.small.length} 个`);
    console.log(`中按钮 (25-35px): ${dimensionGroups.medium.length} 个`);
    console.log(`大按钮 (>35px): ${dimensionGroups.large.length} 个`);

    // 保存分组结果
    const fs = require('fs');
    fs.writeFileSync('button_dimensions_grouped.json', JSON.stringify(dimensionGroups, null, 2), 'utf8');
    console.log('✅ 按钮尺寸分组已保存到: button_dimensions_grouped.json');

    return dimensionGroups;
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--button-dimensions')) {
        filterButtonDimensions();
    } else {
        await demonstrateFiltering();
    }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main();
}

module.exports = { 
    demonstrateFiltering, 
    filterButtonDimensions, 
    createDimensionSummary,
    saveForLLMAnalysis 
};
